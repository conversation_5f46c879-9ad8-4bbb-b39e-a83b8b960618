import React, { useState, useReducer, useCallback, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { ArrowLeft, Eye, Code, Save, Send, Download, Undo, Redo } from 'lucide-react'

// Enhanced Template Editor Components
import { TemplateEditorCanvas } from '../components/TemplateEditor/TemplateEditorCanvas'
import { ComponentLibrary } from '../components/TemplateEditor/ComponentLibrary'
import { PropertiesPanel } from '../components/TemplateEditor/PropertiesPanel'

// Types
import { TemplateComponent, TemplateEditorState, TemplateEditorAction } from '../types/templateEditor'

// Modals
import { PreviewModal } from '../components/TemplateEditor/PreviewModal'
import { EmailInputModal } from '../components/TemplateEditor/EmailInputModal'

// Notifications
import { useNotifications } from '../hooks/useNotifications'

// Template Editor Reducer
const templateEditorReducer = (state: TemplateEditorState, action: TemplateEditorAction): TemplateEditorState => {
  switch (action.type) {
    case 'ADD_COMPONENT':
      const newComponents = [...state.components, action.payload]
      return {
        ...state,
        components: newComponents,
        history: [...state.history.slice(0, state.historyIndex + 1), newComponents],
        historyIndex: state.historyIndex + 1,
        isDirty: true
      }
    case 'UPDATE_COMPONENT':
      const updatedComponents = state.components.map(c => 
        c.id === action.payload.id ? { ...c, ...action.payload.updates } : c
      )
      return {
        ...state,
        components: updatedComponents,
        history: [...state.history.slice(0, state.historyIndex + 1), updatedComponents],
        historyIndex: state.historyIndex + 1,
        isDirty: true
      }
    case 'DELETE_COMPONENT':
      const filteredComponents = state.components.filter(c => c.id !== action.payload)
      return {
        ...state,
        components: filteredComponents,
        history: [...state.history.slice(0, state.historyIndex + 1), filteredComponents],
        historyIndex: state.historyIndex + 1,
        isDirty: true
      }
    case 'SELECT_COMPONENT':
      return { ...state, selectedComponent: action.payload }
    case 'SET_ZOOM':
      return { ...state, zoom: action.payload }
    case 'TOGGLE_GRID':
      return { ...state, showGrid: !state.showGrid }
    case 'SET_PREVIEW_MODE':
      return { ...state, previewMode: action.payload }
    case 'UNDO':
      if (state.historyIndex > 0) {
        return {
          ...state,
          components: state.history[state.historyIndex - 1],
          historyIndex: state.historyIndex - 1,
          isDirty: true
        }
      }
      return state
    case 'REDO':
      if (state.historyIndex < state.history.length - 1) {
        return {
          ...state,
          components: state.history[state.historyIndex + 1],
          historyIndex: state.historyIndex + 1,
          isDirty: true
        }
      }
      return state
    case 'RESET_DIRTY':
      return { ...state, isDirty: false }
    case 'LOAD_TEMPLATE':
      return {
        ...state,
        components: action.payload,
        history: [action.payload],
        historyIndex: 0,
        isDirty: false
      }
    default:
      return state
  }
}

const EnhancedTemplateEditor: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const isEditing = Boolean(id)
  const { success, error: showError } = useNotifications()

  // Template metadata
  const [templateName, setTemplateName] = useState('')
  const [templateDescription, setTemplateDescription] = useState('')
  const [viewMode, setViewMode] = useState<'visual' | 'code'>('visual')
  const [htmlContent, setHtmlContent] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [showEmailModal, setShowEmailModal] = useState(false)
  const [sendingTestEmail, setSendingTestEmail] = useState(false)

  // Template editor state
  const [state, dispatch] = useReducer(templateEditorReducer, {
    components: [],
    selectedComponent: null,
    history: [[]],
    historyIndex: 0,
    zoom: 100,
    showGrid: false,
    previewMode: 'desktop',
    isDirty: false
  })

  // Loading effect
  useEffect(() => {
    if (isLoading) {
      document.body.classList.add('cursor-wait')
    } else {
      document.body.classList.remove('cursor-wait')
    }
    return () => {
      document.body.classList.remove('cursor-wait')
    }
  }, [isLoading])

  // Load template if editing
  useEffect(() => {
    if (isEditing && id) {
      loadTemplate(id)
    }
  }, [id, isEditing])

  const loadTemplate = async (templateId: string) => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/templates/${templateId}`)
      if (response.ok) {
        const template = await response.json()
        setTemplateName(template.name || '')
        setTemplateDescription(template.description || '')
        if (template.components) {
          dispatch({ type: 'LOAD_TEMPLATE', payload: template.components })
        }
        if (template.html_content) {
          setHtmlContent(template.html_content)
        }
      }
    } catch (error) {
      console.error('Failed to load template:', error)
      showError('Failed to load template')
    } finally {
      setIsLoading(false)
    }
  }

  // Check for unsaved changes
  const hasUnsavedChanges = useCallback(() => {
    return state.isDirty || templateName.trim() !== '' || templateDescription.trim() !== ''
  }, [state.isDirty, templateName, templateDescription])

  // Warn user about unsaved changes when leaving the page
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges()) {
        e.preventDefault()
        return (e.returnValue = 'You have unsaved changes. Are you sure you want to leave?')
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasUnsavedChanges])

  // Generate HTML from components
  const generateHTML = useCallback(() => {
    if (viewMode === 'code') {
      return htmlContent
    }

    // Convert components to HTML
    const componentsHTML = state.components.map(component => {
      switch (component.type) {
        case 'text':
          return `<p style="${Object.entries(component.styles).map(([key, value]) => `${key}: ${value}`).join('; ')}">${component.content.text || ''}</p>`
        case 'heading':
          return `<h2 style="${Object.entries(component.styles).map(([key, value]) => `${key}: ${value}`).join('; ')}">${component.content.text || ''}</h2>`
        case 'image':
          return `<img src="${component.content.src || ''}" alt="${component.content.alt || ''}" style="${Object.entries(component.styles).map(([key, value]) => `${key}: ${value}`).join('; ')}" />`
        case 'button':
          return `<a href="${component.content.href || '#'}" style="display: inline-block; ${Object.entries(component.styles).map(([key, value]) => `${key}: ${value}`).join('; ')}">${component.content.text || ''}</a>`
        case 'divider':
          return `<hr style="${Object.entries(component.styles).map(([key, value]) => `${key}: ${value}`).join('; ')}" />`
        case 'spacer':
          return `<div style="height: ${component.styles.height || '20px'}; ${Object.entries(component.styles).map(([key, value]) => `${key}: ${value}`).join('; ')}"></div>`
        default:
          return ''
      }
    }).join('\n')

    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>${templateName || 'Email Template'}</title>
</head>
<body style="margin: 0; padding: 20px; font-family: Arial, sans-serif;">
  ${componentsHTML}
</body>
</html>`
  }, [state.components, viewMode, htmlContent, templateName])

  const handleViewModeChange = (mode: 'visual' | 'code') => {
    if (mode === 'code' && viewMode === 'visual') {
      setHtmlContent(generateHTML())
    }
    setViewMode(mode)
  }

  const handleHtmlContentChange = (content: string) => {
    setHtmlContent(content)
  }

  // Auto-save functionality
  useEffect(() => {
    if (!state.isDirty && !templateName && !templateDescription) return

    const autoSaveTimer = setTimeout(async () => {
      try {
        const templateData = {
          name: templateName,
          description: templateDescription,
          components: state.components,
          html_content: generateHTML(),
          updated_at: new Date().toISOString()
        }

        if (isEditing && id) {
          await fetch(`/api/templates/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(templateData)
          })
        } else {
          const response = await fetch('/api/templates', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(templateData)
          })
          if (response.ok) {
            const newTemplate = await response.json()
            navigate(`/templates/${newTemplate.id}/edit`, { replace: true })
          }
        }

        dispatch({ type: 'RESET_DIRTY' })
      } catch (error) {
        console.error('Auto-save failed:', error)
      }
    }, 2000) // Auto-save after 2 seconds of inactivity

    return () => clearTimeout(autoSaveTimer)
  }, [state.isDirty, state.components, templateName, templateDescription, generateHTML, id, isEditing, navigate])

  // Event handlers
  const handleComponentAdd = useCallback((component: TemplateComponent) => {
    dispatch({ type: 'ADD_COMPONENT', payload: component })
  }, [])

  const handleComponentUpdate = useCallback((id: string, updates: Partial<TemplateComponent>) => {
    dispatch({ type: 'UPDATE_COMPONENT', payload: { id, updates } })
  }, [])

  const handleComponentDelete = useCallback((id: string) => {
    dispatch({ type: 'DELETE_COMPONENT', payload: id })
  }, [])

  const handleComponentSelect = useCallback((id: string | null) => {
    dispatch({ type: 'SELECT_COMPONENT', payload: id })
  }, [])

  const handleZoomChange = useCallback((zoom: number) => {
    dispatch({ type: 'SET_ZOOM', payload: zoom })
  }, [])

  const handleToggleGrid = useCallback(() => {
    dispatch({ type: 'TOGGLE_GRID' })
  }, [])

  const handlePreviewModeChange = useCallback((mode: 'desktop' | 'tablet' | 'mobile') => {
    dispatch({ type: 'SET_PREVIEW_MODE', payload: mode })
  }, [])

  const handleUndo = useCallback(() => {
    dispatch({ type: 'UNDO' })
  }, [])

  const handleRedo = useCallback(() => {
    dispatch({ type: 'REDO' })
  }, [])

  const handleSave = async () => {
    try {
      setIsLoading(true)
      const templateData = {
        name: templateName,
        description: templateDescription,
        components: state.components,
        html_content: generateHTML(),
        updated_at: new Date().toISOString()
      }

      let response
      if (isEditing && id) {
        response = await fetch(`/api/templates/${id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(templateData)
        })
      } else {
        response = await fetch('/api/templates', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(templateData)
        })
      }

      if (response.ok) {
        dispatch({ type: 'RESET_DIRTY' })
        success('Template saved successfully!')
        
        if (!isEditing) {
          const newTemplate = await response.json()
          navigate(`/templates/${newTemplate.id}/edit`, { replace: true })
        }
      } else {
        throw new Error('Failed to save template')
      }
    } catch (error) {
      console.error('Save failed:', error)
      showError('Failed to save template')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePreview = () => {
    setShowPreview(true)
  }

  const handleExport = () => {
    const html = generateHTML()
    const blob = new Blob([html], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${templateName || 'template'}.html`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleSendTest = () => {
    setShowEmailModal(true)
  }

  const handleSendTestEmail = async (email: string) => {
    try {
      setSendingTestEmail(true)
      const response = await fetch('/api/send-test-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: email,
          subject: `Test: ${templateName || 'Email Template'}`,
          html: generateHTML()
        })
      })

      if (response.ok) {
        success('Test email sent successfully!')
        setShowEmailModal(false)
      } else {
        throw new Error('Failed to send test email')
      }
    } catch (error) {
      console.error('Send test email failed:', error)
      showError('Failed to send test email')
    } finally {
      setSendingTestEmail(false)
    }
  }

  const selectedComponentData = state.selectedComponent
    ? state.components.find(c => c.id === state.selectedComponent) || null
    : null
