import React, { useState } from 'react'
import { useDrag } from 'react-dnd'
import {
  Type,
  Image,
  MousePointer,
  Minus,
  Square,
  Layout,
  Mail,
  ShoppingCart,
  Users,
  ChevronDown,
  ChevronRight,
  Plus,
  Grid3X3,
  Palette
} from 'lucide-react'

interface EnhancedComponentLibraryProps {
  onComponentAdd: (component: any) => void
}

interface ComponentCategory {
  id: string
  name: string
  icon: React.ReactNode
  components: ComponentDefinition[]
}

interface ComponentDefinition {
  id: string
  name: string
  icon: React.ReactNode
  componentType: string
  defaultContent: any
  defaultStyles: any
  preview: string
  thumbnail?: string
}

const DraggableComponent: React.FC<{
  component: ComponentDefinition
  onAdd: (component: ComponentDefinition) => void
}> = ({ component, onAdd }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'component',
    item: { component },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }))

  return (
    <div
      ref={drag}
      className={`group relative bg-white border border-gray-200 rounded-lg p-4 cursor-grab hover:border-teal-300 hover:shadow-md transition-all duration-200 ${
        isDragging ? 'opacity-50 rotate-2 scale-105' : ''
      }`}
      onClick={() => onAdd(component)}
    >
      {/* Component Preview */}
      <div className="h-16 bg-gray-50 rounded-md mb-3 flex items-center justify-center border border-gray-100">
        {component.thumbnail ? (
          <img src={component.thumbnail} alt={component.name} className="h-full w-full object-cover rounded-md" />
        ) : (
          <div 
            className="text-xs text-gray-500 text-center"
            dangerouslySetInnerHTML={{ __html: component.preview }}
          />
        )}
      </div>

      {/* Component Info */}
      <div className="flex items-center space-x-2">
        <div className="text-teal-600">
          {component.icon}
        </div>
        <span className="text-sm font-medium text-gray-700 group-hover:text-gray-900">
          {component.name}
        </span>
      </div>

      {/* Hover Add Button */}
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <button
          onClick={(e) => {
            e.stopPropagation()
            onAdd(component)
          }}
          className="w-6 h-6 bg-teal-500 text-white rounded-full flex items-center justify-center hover:bg-teal-600 transition-colors"
        >
          <Plus className="w-3 h-3" />
        </button>
      </div>

      {/* Drag Indicator */}
      <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <Grid3X3 className="w-4 h-4 text-gray-400" />
      </div>
    </div>
  )
}

export const EnhancedComponentLibrary: React.FC<EnhancedComponentLibraryProps> = ({ onComponentAdd }) => {
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['basic', 'content'])

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => 
      prev.includes(categoryId) 
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    )
  }

  const componentCategories: ComponentCategory[] = [
    {
      id: 'basic',
      name: 'Basic Elements',
      icon: <Type className="w-4 h-4" />,
      components: [
        {
          id: 'text',
          name: 'Text Block',
          icon: <Type className="w-4 h-4 text-blue-600" />,
          componentType: 'text',
          defaultContent: {
            text: 'Your text content goes here. Click to edit this text and make it your own.'
          },
          defaultStyles: {
            fontSize: '16px',
            lineHeight: '1.5',
            color: '#333333',
            padding: '16px',
            fontFamily: 'Arial, sans-serif'
          },
          preview: '<div style="padding: 4px; font-size: 10px; color: #666;">Lorem ipsum dolor sit amet...</div>'
        },
        {
          id: 'heading',
          name: 'Heading',
          icon: <Type className="w-4 h-4 text-purple-600" />,
          componentType: 'heading',
          defaultContent: {
            text: 'Your Heading Text'
          },
          defaultStyles: {
            fontSize: '32px',
            fontWeight: 'bold',
            color: '#1a1a1a',
            padding: '16px',
            textAlign: 'center',
            fontFamily: 'Arial, sans-serif'
          },
          preview: '<div style="font-size: 12px; font-weight: bold; text-align: center; padding: 4px;">Heading</div>'
        },
        {
          id: 'image',
          name: 'Image',
          icon: <Image className="w-4 h-4 text-green-600" />,
          componentType: 'image',
          defaultContent: {
            src: 'https://via.placeholder.com/600x300',
            alt: 'Placeholder image'
          },
          defaultStyles: {
            width: '100%',
            height: 'auto',
            borderRadius: '8px'
          },
          preview: '<div style="background: #e5e7eb; height: 40px; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 10px; color: #6b7280;">📷 Image</div>'
        },
        {
          id: 'button',
          name: 'Button',
          icon: <MousePointer className="w-4 h-4 text-orange-600" />,
          componentType: 'button',
          defaultContent: {
            text: 'Click Here',
            href: '#'
          },
          defaultStyles: {
            backgroundColor: '#3b82f6',
            color: '#ffffff',
            padding: '12px 24px',
            borderRadius: '6px',
            textDecoration: 'none',
            display: 'inline-block',
            fontWeight: 'bold',
            textAlign: 'center'
          },
          preview: '<div style="background: #3b82f6; color: white; padding: 4px 8px; border-radius: 4px; text-align: center; font-size: 10px;">Button</div>'
        },
        {
          id: 'divider',
          name: 'Divider',
          icon: <Minus className="w-4 h-4 text-gray-600" />,
          componentType: 'divider',
          defaultContent: {},
          defaultStyles: {
            border: 'none',
            borderTop: '1px solid #e5e7eb',
            margin: '20px 0',
            width: '100%'
          },
          preview: '<div style="border-top: 1px solid #e5e7eb; margin: 8px 0;"></div>'
        },
        {
          id: 'spacer',
          name: 'Spacer',
          icon: <Square className="w-4 h-4 text-gray-500" />,
          componentType: 'spacer',
          defaultContent: {},
          defaultStyles: {
            height: '40px',
            width: '100%'
          },
          preview: '<div style="height: 20px; border: 1px dashed #d1d5db; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 8px; color: #9ca3af;">SPACE</div>'
        }
      ]
    },
    {
      id: 'content',
      name: 'Content Blocks',
      icon: <Mail className="w-4 h-4" />,
      components: [
        {
          id: 'header',
          name: 'Header',
          icon: <Layout className="w-4 h-4 text-blue-600" />,
          componentType: 'header',
          defaultContent: {
            logo: 'https://via.placeholder.com/120x40',
            title: 'Your Company',
            navigation: ['Home', 'About', 'Services', 'Contact']
          },
          defaultStyles: {
            backgroundColor: '#1f2937',
            color: '#ffffff',
            padding: '20px',
            textAlign: 'center'
          },
          preview: '<div style="background: #1f2937; color: white; padding: 8px; text-align: center; font-size: 10px; border-radius: 4px;">🏢 Header</div>'
        },
        {
          id: 'footer',
          name: 'Footer',
          icon: <Layout className="w-4 h-4 text-gray-600" />,
          componentType: 'footer',
          defaultContent: {
            companyName: 'Your Company',
            address: '123 Main St, City, State 12345',
            links: ['Privacy Policy', 'Terms of Service', 'Unsubscribe']
          },
          defaultStyles: {
            backgroundColor: '#f3f4f6',
            color: '#6b7280',
            padding: '20px',
            textAlign: 'center',
            fontSize: '14px'
          },
          preview: '<div style="background: #f3f4f6; color: #6b7280; padding: 8px; text-align: center; font-size: 10px; border-radius: 4px;">📄 Footer</div>'
        },
        {
          id: 'product-card',
          name: 'Product Card',
          icon: <ShoppingCart className="w-4 h-4 text-green-600" />,
          componentType: 'product-card',
          defaultContent: {
            image: 'https://via.placeholder.com/300x200',
            title: 'Product Name',
            description: 'Product description goes here',
            price: '$99.99',
            buttonText: 'Buy Now'
          },
          defaultStyles: {
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            padding: '16px',
            textAlign: 'center',
            backgroundColor: '#ffffff'
          },
          preview: '<div style="border: 1px solid #e5e7eb; border-radius: 4px; padding: 8px; text-align: center; font-size: 10px;">🛍️ Product</div>'
        },
        {
          id: 'social-media',
          name: 'Social Media',
          icon: <Users className="w-4 h-4 text-blue-500" />,
          componentType: 'social-media',
          defaultContent: {
            platforms: ['facebook', 'twitter', 'instagram', 'linkedin']
          },
          defaultStyles: {
            textAlign: 'center',
            padding: '20px'
          },
          preview: '<div style="text-align: center; font-size: 10px; padding: 8px;">🔗 Social Links</div>'
        }
      ]
    }
  ]

  const handleComponentAdd = (component: ComponentDefinition) => {
    const newComponent = {
      id: `component-${Date.now()}`,
      type: component.componentType,
      content: component.defaultContent,
      styles: {
        ...component.defaultStyles,
        position: 'relative',
        zIndex: 1
      },
      position: { x: 0, y: 0 }
    }
    onComponentAdd(newComponent)
  }

  return (
    <div className="w-80 bg-gray-800 border-r border-gray-700 flex flex-col shadow-lg">
      {/* Header */}
      <div className="p-6 border-b border-gray-700">
        <h2 className="text-white font-semibold text-lg mb-2">Components</h2>
        <p className="text-gray-400 text-sm">Drag components to the canvas or click to add</p>
      </div>

      {/* Component Categories */}
      <div className="flex-1 overflow-y-auto">
        {componentCategories.map((category) => (
          <div key={category.id} className="border-b border-gray-700 last:border-b-0">
            {/* Category Header */}
            <button
              onClick={() => toggleCategory(category.id)}
              className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-700 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <div className="text-teal-400">
                  {category.icon}
                </div>
                <span className="text-white font-medium">{category.name}</span>
              </div>
              <div className="text-gray-400">
                {expandedCategories.includes(category.id) ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
              </div>
            </button>

            {/* Category Components */}
            {expandedCategories.includes(category.id) && (
              <div className="p-4 space-y-3 bg-gray-750">
                {category.components.map((component) => (
                  <DraggableComponent
                    key={component.id}
                    component={component}
                    onAdd={handleComponentAdd}
                  />
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center justify-center text-gray-400 text-sm">
          <Palette className="w-4 h-4 mr-2" />
          <span>Drag & Drop to Build</span>
        </div>
      </div>
    </div>
  )
}
