/* Enhanced Template Editor Styles */

/* Custom Slider Styles */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.slider::-webkit-slider-track {
  background: #374151;
  height: 8px;
  border-radius: 4px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: #14b8a6;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.slider::-webkit-slider-thumb:hover {
  background: #0d9488;
  transform: scale(1.1);
}

.slider::-moz-range-track {
  background: #374151;
  height: 8px;
  border-radius: 4px;
  border: none;
}

.slider::-moz-range-thumb {
  background: #14b8a6;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.slider::-moz-range-thumb:hover {
  background: #0d9488;
  transform: scale(1.1);
}

/* Dark theme scrollbar */
.bg-gray-750 {
  background-color: #2d3748;
}

/* Smooth transitions for all interactive elements */
.transition-all {
  transition: all 0.2s ease-in-out;
}

/* Enhanced hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Professional gradient backgrounds */
.gradient-teal {
  background: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
}

.gradient-gray {
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
}

/* Enhanced focus states */
.focus-ring:focus {
  outline: none;
  ring: 2px;
  ring-color: #14b8a6;
  ring-opacity: 0.5;
}

/* Custom scrollbar for dark theme */
.dark-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.dark-scrollbar::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 4px;
}

.dark-scrollbar::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 4px;
}

.dark-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Enhanced button styles */
.btn-primary {
  background: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px rgba(20, 184, 166, 0.2);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(20, 184, 166, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(20, 184, 166, 0.2);
}

.btn-secondary {
  background: #374151;
  color: #d1d5db;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #4b5563;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.btn-secondary:hover {
  background: #4b5563;
  color: #f9fafb;
  border-color: #6b7280;
}

/* Enhanced input styles */
.input-dark {
  background: #374151;
  border: 1px solid #4b5563;
  color: #f9fafb;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease-in-out;
}

.input-dark:focus {
  outline: none;
  border-color: #14b8a6;
  box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.1);
}

.input-dark::placeholder {
  color: #9ca3af;
}

/* Professional card styles */
.card-dark {
  background: #1f2937;
  border: 1px solid #374151;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-dark:hover {
  border-color: #4b5563;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Enhanced tooltip styles */
.tooltip {
  position: relative;
}

.tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: #f9fafb;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease-in-out;
  z-index: 1000;
}

.tooltip:hover::before {
  opacity: 1;
}

/* Loading spinner */
.spinner {
  border: 2px solid #374151;
  border-top: 2px solid #14b8a6;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced grid overlay */
.grid-overlay {
  background-image: 
    linear-gradient(rgba(20, 184, 166, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(20, 184, 166, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Professional shadows */
.shadow-soft {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.shadow-medium {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.shadow-strong {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.16);
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-full {
    width: 100%;
  }
}
