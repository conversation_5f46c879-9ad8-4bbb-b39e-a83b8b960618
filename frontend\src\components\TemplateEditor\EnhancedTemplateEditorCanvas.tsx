import React, { useRef, useState, useCallback } from 'react'
import { useDrop } from 'react-dnd'
import { TemplateComponent } from '../../types/templateEditor'
import { 
  Code, 
  Copy, 
  Trash2, 
  GripVertical, 
  Edit3,
  Eye,
  EyeOff
} from 'lucide-react'

interface EnhancedTemplateEditorCanvasProps {
  components: TemplateComponent[]
  selectedComponent: string | null
  onComponentSelect: (id: string | null) => void
  onComponentUpdate: (id: string, updates: Partial<TemplateComponent>) => void
  onComponentAdd: (component: TemplateComponent) => void
  onComponentDelete: (id: string) => void
  onComponentDuplicate?: (id: string) => void
  onCodeEdit?: (id: string) => void
  zoom: number
  onZoomChange: (zoom: number) => void
  showGrid: boolean
  previewMode: 'desktop' | 'tablet' | 'mobile'
}

interface ModuleHoverControlsProps {
  component: TemplateComponent
  onEdit: () => void
  onDuplicate: () => void
  onDelete: () => void
  onCodeEdit?: () => void
  position: { top: number; height: number }
}

const ModuleHoverControls: React.FC<ModuleHoverControlsProps> = ({
  component,
  onEdit,
  onDuplicate,
  onDelete,
  onCodeEdit,
  position
}) => {
  return (
    <div 
      className="absolute right-0 flex flex-col space-y-1 z-50 opacity-0 group-hover:opacity-100 transition-all duration-200 ease-out"
      style={{ 
        top: position.top + position.height / 2 - 80, 
        transform: 'translateX(100%)' 
      }}
    >
      {/* Code Edit Button */}
      {onCodeEdit && (
        <button
          onClick={onCodeEdit}
          className="w-10 h-10 bg-gray-600 hover:bg-teal-500 text-white rounded-l-lg flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg"
          title="Edit Code"
        >
          <Code className="w-4 h-4" />
        </button>
      )}

      {/* Duplicate Button */}
      <button
        onClick={onDuplicate}
        className="w-10 h-10 bg-gray-600 hover:bg-blue-500 text-white rounded-l-lg flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg"
        title="Duplicate"
      >
        <Copy className="w-4 h-4" />
      </button>

      {/* Drag Handle */}
      <button
        className="w-10 h-10 bg-gray-600 hover:bg-orange-500 text-white rounded-l-lg flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg cursor-grab active:cursor-grabbing"
        title="Drag to Reorder"
      >
        <GripVertical className="w-4 h-4" />
      </button>

      {/* Delete Button */}
      <button
        onClick={onDelete}
        className="w-10 h-10 bg-gray-600 hover:bg-red-500 text-white rounded-l-lg flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg"
        title="Delete"
      >
        <Trash2 className="w-4 h-4" />
      </button>
    </div>
  )
}

const ComponentRenderer: React.FC<{
  component: TemplateComponent
  isSelected: boolean
  onSelect: () => void
  onUpdate: (updates: Partial<TemplateComponent>) => void
  onDelete: () => void
  onDuplicate: () => void
  onCodeEdit?: () => void
}> = ({ component, isSelected, onSelect, onUpdate, onDelete, onDuplicate, onCodeEdit }) => {
  const [elementRef, setElementRef] = useState<HTMLDivElement | null>(null)
  const [isHovered, setIsHovered] = useState(false)

  const getPosition = useCallback(() => {
    if (!elementRef) return { top: 0, height: 0 }
    const rect = elementRef.getBoundingClientRect()
    return { top: 0, height: rect.height }
  }, [elementRef])

  const renderComponent = () => {
    const baseStyles = {
      ...component.styles,
      outline: isSelected ? '2px solid #14b8a6' : 'none',
      outlineOffset: isSelected ? '2px' : '0',
      position: 'relative' as const
    }

    switch (component.type) {
      case 'text':
        return (
          <div
            style={baseStyles}
            dangerouslySetInnerHTML={{ __html: component.content.text || 'Click to edit text' }}
          />
        )
      case 'heading':
        return (
          <h2 style={baseStyles}>
            {component.content.text || 'Click to edit heading'}
          </h2>
        )
      case 'image':
        return (
          <img
            src={component.content.src || 'https://via.placeholder.com/600x300'}
            alt={component.content.alt || ''}
            style={baseStyles}
          />
        )
      case 'button':
        return (
          <a
            href={component.content.href || '#'}
            style={{
              ...baseStyles,
              display: 'inline-block',
              textDecoration: 'none'
            }}
          >
            {component.content.text || 'Button Text'}
          </a>
        )
      case 'divider':
        return <hr style={baseStyles} />
      case 'spacer':
        return <div style={baseStyles} />
      case 'header':
        return (
          <header style={baseStyles}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <img 
                src={component.content.logo || 'https://via.placeholder.com/120x40'} 
                alt="Logo" 
                style={{ height: '40px' }}
              />
              <h1>{component.content.title || 'Your Company'}</h1>
            </div>
          </header>
        )
      case 'footer':
        return (
          <footer style={baseStyles}>
            <p>{component.content.companyName || 'Your Company'}</p>
            <p>{component.content.address || '123 Main St, City, State 12345'}</p>
          </footer>
        )
      case 'product-card':
        return (
          <div style={baseStyles}>
            <img 
              src={component.content.image || 'https://via.placeholder.com/300x200'} 
              alt={component.content.title || 'Product'} 
              style={{ width: '100%', height: 'auto' }}
            />
            <h3>{component.content.title || 'Product Name'}</h3>
            <p>{component.content.description || 'Product description'}</p>
            <p style={{ fontWeight: 'bold' }}>{component.content.price || '$99.99'}</p>
            <button>{component.content.buttonText || 'Buy Now'}</button>
          </div>
        )
      case 'social-media':
        return (
          <div style={baseStyles}>
            <div style={{ display: 'flex', justifyContent: 'center', gap: '10px' }}>
              {(component.content.platforms || ['facebook', 'twitter', 'instagram']).map((platform: string) => (
                <a key={platform} href="#" style={{ textDecoration: 'none' }}>
                  {platform.charAt(0).toUpperCase() + platform.slice(1)}
                </a>
              ))}
            </div>
          </div>
        )
      default:
        return <div style={baseStyles}>Unknown component type</div>
    }
  }

  return (
    <div
      ref={setElementRef}
      className="group relative"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onSelect}
    >
      {renderComponent()}
      
      {/* Module Hover Controls */}
      {isHovered && (
        <ModuleHoverControls
          component={component}
          onEdit={onSelect}
          onDuplicate={onDuplicate}
          onDelete={onDelete}
          onCodeEdit={onCodeEdit}
          position={getPosition()}
        />
      )}
    </div>
  )
}

export const EnhancedTemplateEditorCanvas: React.FC<EnhancedTemplateEditorCanvasProps> = ({
  components,
  selectedComponent,
  onComponentSelect,
  onComponentUpdate,
  onComponentAdd,
  onComponentDelete,
  onComponentDuplicate,
  onCodeEdit,
  zoom,
  onZoomChange,
  showGrid,
  previewMode
}) => {
  const canvasRef = useRef<HTMLDivElement>(null)
  const [showCodeEditor, setShowCodeEditor] = useState<string | null>(null)

  const [{ isOver }, drop] = useDrop(() => ({
    accept: 'component',
    drop: (item: { component: any }) => {
      const newComponent = {
        id: `component-${Date.now()}`,
        type: item.component.componentType,
        content: item.component.defaultContent,
        styles: {
          ...item.component.defaultStyles,
          position: 'relative',
          zIndex: 1
        },
        position: { x: 0, y: 0 }
      }
      onComponentAdd(newComponent)
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  }))

  const getCanvasWidth = () => {
    switch (previewMode) {
      case 'mobile': return '375px'
      case 'tablet': return '768px'
      default: return '100%'
    }
  }

  const handleComponentDuplicate = (componentId: string) => {
    const component = components.find(c => c.id === componentId)
    if (component) {
      const duplicatedComponent = {
        ...component,
        id: `component-${Date.now()}`,
      }
      onComponentAdd(duplicatedComponent)
    }
  }



  return (
    <div className="flex-1 bg-gray-100 overflow-auto relative">
      {/* Canvas Controls */}
      <div className="absolute top-4 right-4 z-40 flex items-center space-x-2">
        {/* Zoom Controls */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 flex items-center">
          <button
            onClick={() => onZoomChange(Math.max(25, zoom - 25))}
            className="px-3 py-2 text-gray-600 hover:text-gray-800 border-r border-gray-200"
          >
            -
          </button>
          <span className="px-3 py-2 text-sm font-medium text-gray-700 min-w-[60px] text-center">
            {zoom}%
          </span>
          <button
            onClick={() => onZoomChange(Math.min(200, zoom + 25))}
            className="px-3 py-2 text-gray-600 hover:text-gray-800 border-l border-gray-200"
          >
            +
          </button>
        </div>

        {/* Grid Toggle */}
        <button
          onClick={() => {}} // Grid toggle functionality
          className={`p-2 rounded-lg border transition-colors ${
            showGrid 
              ? 'bg-teal-500 text-white border-teal-500' 
              : 'bg-white text-gray-600 border-gray-200 hover:text-gray-800'
          }`}
          title="Toggle Grid"
        >
          {showGrid ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
        </button>
      </div>

      {/* Canvas */}
      <div 
        ref={drop}
        className={`min-h-full p-8 transition-all duration-200 ${
          isOver ? 'bg-teal-50 border-2 border-dashed border-teal-300' : ''
        }`}
      >
        <div
          ref={canvasRef}
          className="mx-auto bg-white shadow-lg rounded-lg overflow-hidden transition-all duration-300"
          style={{
            width: getCanvasWidth(),
            transform: `scale(${zoom / 100})`,
            transformOrigin: 'top center',
            minHeight: '600px'
          }}
        >
          {/* Grid Overlay */}
          {showGrid && (
            <div 
              className="absolute inset-0 pointer-events-none opacity-20"
              style={{
                backgroundImage: 'linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)',
                backgroundSize: '20px 20px'
              }}
            />
          )}

          {/* Empty State */}
          {components.length === 0 && (
            <div className="h-96 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <div className="text-4xl mb-4">📧</div>
                <h3 className="text-lg font-medium mb-2">Start Building Your Email</h3>
                <p className="text-sm">Drag components from the sidebar or click to add them</p>
              </div>
            </div>
          )}

          {/* Components */}
          <div className="relative">
            {components.map((component, index) => (
              <ComponentRenderer
                key={component.id}
                component={component}
                isSelected={selectedComponent === component.id}
                onSelect={() => onComponentSelect(component.id)}
                onUpdate={(updates) => onComponentUpdate(component.id, updates)}
                onDelete={() => onComponentDelete(component.id)}
                onDuplicate={() => handleComponentDuplicate(component.id)}
                onCodeEdit={onCodeEdit ? () => onCodeEdit(component.id) : undefined}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
