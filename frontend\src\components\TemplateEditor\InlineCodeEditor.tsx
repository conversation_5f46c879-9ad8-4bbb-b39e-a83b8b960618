import React, { useState, useEffect, useRef } from 'react'
import { X, Save, RotateCcw, Code, Eye } from 'lucide-react'

interface InlineCodeEditorProps {
  isOpen: boolean
  onClose: () => void
  onSave: (html: string) => void
  initialHtml: string
  componentType: string
  componentId: string
}

export const InlineCodeEditor: React.FC<InlineCodeEditorProps> = ({
  isOpen,
  onClose,
  onSave,
  initialHtml,
  componentType,
  componentId
}) => {
  const [htmlContent, setHtmlContent] = useState(initialHtml)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    setHtmlContent(initialHtml)
    setHasChanges(false)
  }, [initialHtml, isOpen])

  useEffect(() => {
    setHasChanges(htmlContent !== initialHtml)
  }, [htmlContent, initialHtml])

  const handleSave = () => {
    onSave(htmlContent)
    setHasChanges(false)
  }

  const handleReset = () => {
    setHtmlContent(initialHtml)
    setHasChanges(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Save with Ctrl+S or Cmd+S
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
      e.preventDefault()
      handleSave()
    }
    
    // Close with Escape
    if (e.key === 'Escape') {
      if (hasChanges) {
        const confirmed = window.confirm('You have unsaved changes. Are you sure you want to close?')
        if (confirmed) {
          onClose()
        }
      } else {
        onClose()
      }
    }

    // Tab handling for better code editing
    if (e.key === 'Tab') {
      e.preventDefault()
      const textarea = e.target as HTMLTextAreaElement
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const value = textarea.value

      // Insert tab character
      const newValue = value.substring(0, start) + '  ' + value.substring(end)
      setHtmlContent(newValue)

      // Move cursor
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + 2
      }, 0)
    }
  }

  const insertTag = (tag: string) => {
    if (!textareaRef.current) return

    const textarea = textareaRef.current
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = htmlContent.substring(start, end)
    
    let insertion = ''
    if (selectedText) {
      insertion = `<${tag}>${selectedText}</${tag}>`
    } else {
      insertion = `<${tag}></${tag}>`
    }

    const newValue = htmlContent.substring(0, start) + insertion + htmlContent.substring(end)
    setHtmlContent(newValue)

    // Position cursor
    setTimeout(() => {
      const newPosition = selectedText ? start + insertion.length : start + tag.length + 2
      textarea.selectionStart = textarea.selectionEnd = newPosition
      textarea.focus()
    }, 0)
  }

  const formatCode = () => {
    // Simple HTML formatting
    let formatted = htmlContent
      .replace(/></g, '>\n<')
      .replace(/^\s+|\s+$/gm, '')
      .split('\n')
      .map((line, index) => {
        const depth = (line.match(/^<(?!\/)/g) || []).length - (line.match(/<\//g) || []).length
        return '  '.repeat(Math.max(0, depth)) + line.trim()
      })
      .join('\n')
    
    setHtmlContent(formatted)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-4xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-800 rounded flex items-center justify-center">
              <Code className="w-4 h-4 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-800">Code Editor</h2>
              <p className="text-sm text-gray-600">
                Editing {componentType} component ({componentId})
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Preview Toggle */}
            <button
              onClick={() => setIsPreviewMode(!isPreviewMode)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                isPreviewMode
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Eye className="w-4 h-4 mr-1 inline" />
              {isPreviewMode ? 'Code' : 'Preview'}
            </button>

            {/* Close Button */}
            <button
              onClick={() => {
                if (hasChanges) {
                  const confirmed = window.confirm('You have unsaved changes. Are you sure you want to close?')
                  if (confirmed) {
                    onClose()
                  }
                } else {
                  onClose()
                }
              }}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Toolbar */}
        <div className="flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            {/* Quick Insert Buttons */}
            <div className="flex items-center space-x-1">
              <span className="text-xs text-gray-500 mr-2">Quick Insert:</span>
              {['p', 'div', 'span', 'a', 'img', 'h1', 'h2', 'h3'].map((tag) => (
                <button
                  key={tag}
                  onClick={() => insertTag(tag)}
                  className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-100 transition-colors"
                >
                  {tag}
                </button>
              ))}
            </div>
            
            <div className="w-px h-4 bg-gray-300" />
            
            {/* Format Button */}
            <button
              onClick={formatCode}
              className="px-3 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-100 transition-colors"
            >
              Format
            </button>
          </div>

          <div className="flex items-center space-x-2">
            {/* Reset Button */}
            <button
              onClick={handleReset}
              disabled={!hasChanges}
              className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <RotateCcw className="w-4 h-4 mr-1" />
              Reset
            </button>

            {/* Save Button */}
            <button
              onClick={handleSave}
              disabled={!hasChanges}
              className="flex items-center px-4 py-2 bg-teal-500 text-white rounded-lg hover:bg-teal-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Save className="w-4 h-4 mr-1" />
              Save Changes
            </button>
          </div>
        </div>

        {/* Editor Content */}
        <div className="flex-1 flex overflow-hidden">
          {isPreviewMode ? (
            /* Preview Mode */
            <div className="flex-1 p-4 overflow-auto bg-white">
              <div className="border border-gray-200 rounded-lg p-4">
                <div 
                  dangerouslySetInnerHTML={{ __html: htmlContent }}
                  className="prose max-w-none"
                />
              </div>
            </div>
          ) : (
            /* Code Editor Mode */
            <div className="flex-1 flex">
              {/* Code Editor */}
              <div className="flex-1 relative">
                <textarea
                  ref={textareaRef}
                  value={htmlContent}
                  onChange={(e) => setHtmlContent(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="w-full h-full p-4 font-mono text-sm border-none resize-none focus:outline-none bg-gray-900 text-green-400"
                  placeholder="Enter your HTML code here..."
                  spellCheck={false}
                />
                
                {/* Line Numbers */}
                <div className="absolute left-0 top-0 bottom-0 w-12 bg-gray-800 border-r border-gray-700 flex flex-col text-xs text-gray-500 pt-4">
                  {htmlContent.split('\n').map((_, index) => (
                    <div key={index} className="h-5 flex items-center justify-end pr-2">
                      {index + 1}
                    </div>
                  ))}
                </div>
              </div>

              {/* Live Preview */}
              <div className="w-1/2 border-l border-gray-200 bg-gray-50">
                <div className="p-3 bg-gray-100 border-b border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700">Live Preview</h3>
                </div>
                <div className="p-4 overflow-auto h-full">
                  <div 
                    dangerouslySetInnerHTML={{ __html: htmlContent }}
                    className="prose max-w-none"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-3 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <span>Lines: {htmlContent.split('\n').length}</span>
            <span>Characters: {htmlContent.length}</span>
            {hasChanges && <span className="text-orange-600 font-medium">● Unsaved changes</span>}
          </div>
          
          <div className="text-xs text-gray-500">
            <kbd className="px-1 py-0.5 bg-gray-200 rounded">Ctrl+S</kbd> to save • 
            <kbd className="px-1 py-0.5 bg-gray-200 rounded ml-1">Esc</kbd> to close
          </div>
        </div>
      </div>
    </div>
  )
}
