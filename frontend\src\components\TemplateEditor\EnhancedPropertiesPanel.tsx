import React, { useState } from 'react'
import { TemplateComponent } from '../../types/templateEditor'
import {
  Type,
  Palette,
  Layout,
  Smartphone,
  Monitor,
  Tablet,
  ChevronDown,
  ChevronRight,
  Image,
  Plus,
  Minus,
  RotateCcw,
  Sliders,
  Box,
  Layers
} from 'lucide-react'

interface EnhancedPropertiesPanelProps {
  selectedComponent: TemplateComponent | null
  onComponentUpdate: (updates: Partial<TemplateComponent>) => void
  previewMode: 'desktop' | 'tablet' | 'mobile'
  onPreviewModeChange: (mode: 'desktop' | 'tablet' | 'mobile') => void
}

interface PropertySectionProps {
  id: string
  title: string
  icon: React.ReactNode
  children: React.ReactNode
  defaultExpanded?: boolean
}

const PropertySection: React.FC<PropertySectionProps> = ({ 
  id, 
  title, 
  icon, 
  children, 
  defaultExpanded = true 
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)

  return (
    <div className="border-b border-gray-700 last:border-b-0">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-700 transition-colors"
      >
        <div className="flex items-center space-x-3">
          <div className="text-teal-400">
            {icon}
          </div>
          <span className="text-white font-medium">{title}</span>
        </div>
        <div className="text-gray-400">
          {isExpanded ? (
            <ChevronDown className="w-4 h-4" />
          ) : (
            <ChevronRight className="w-4 h-4" />
          )}
        </div>
      </button>
      
      {isExpanded && (
        <div className="p-4 space-y-4 bg-gray-750">
          {children}
        </div>
      )}
    </div>
  )
}

const ColorPicker: React.FC<{
  label: string
  value: string
  onChange: (color: string) => void
}> = ({ label, value, onChange }) => {
  const [showPicker, setShowPicker] = useState(false)
  
  const presetColors = [
    '#000000', '#ffffff', '#f3f4f6', '#e5e7eb', '#d1d5db', '#9ca3af',
    '#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16', '#22c55e',
    '#06b6d4', '#3b82f6', '#6366f1', '#8b5cf6', '#d946ef', '#ec4899'
  ]

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-gray-300 mb-2">
        {label}
      </label>
      <div className="flex items-center space-x-2">
        <button
          onClick={() => setShowPicker(!showPicker)}
          className="w-8 h-8 rounded border-2 border-gray-600 shadow-sm"
          style={{ backgroundColor: value }}
        />
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white text-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
          placeholder="#000000"
        />
      </div>
      
      {showPicker && (
        <div className="absolute top-full left-0 mt-2 p-3 bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-50">
          <div className="grid grid-cols-6 gap-2 mb-3">
            {presetColors.map((color) => (
              <button
                key={color}
                onClick={() => {
                  onChange(color)
                  setShowPicker(false)
                }}
                className="w-6 h-6 rounded border border-gray-600 hover:scale-110 transition-transform"
                style={{ backgroundColor: color }}
              />
            ))}
          </div>
          <button
            onClick={() => setShowPicker(false)}
            className="w-full px-3 py-1 text-xs text-gray-400 hover:text-white transition-colors"
          >
            Close
          </button>
        </div>
      )}
    </div>
  )
}

const RangeSlider: React.FC<{
  label: string
  value: number
  onChange: (value: number) => void
  min: number
  max: number
  step?: number
  unit?: string
}> = ({ label, value, onChange, min, max, step = 1, unit = '' }) => {
  return (
    <div>
      <div className="flex items-center justify-between mb-2">
        <label className="text-sm font-medium text-gray-300">{label}</label>
        <span className="text-sm text-gray-400">{value}{unit}</span>
      </div>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange(Number(e.target.value))}
        className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
      />
    </div>
  )
}

const SpacingControl: React.FC<{
  label: string
  values: { top: number; right: number; bottom: number; left: number }
  onChange: (values: { top: number; right: number; bottom: number; left: number }) => void
}> = ({ label, values, onChange }) => {
  const [isLinked, setIsLinked] = useState(true)

  const handleChange = (side: keyof typeof values, value: number) => {
    if (isLinked) {
      onChange({ top: value, right: value, bottom: value, left: value })
    } else {
      onChange({ ...values, [side]: value })
    }
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-2">
        <label className="text-sm font-medium text-gray-300">{label}</label>
        <button
          onClick={() => setIsLinked(!isLinked)}
          className={`p-1 rounded ${isLinked ? 'text-teal-400' : 'text-gray-400'}`}
        >
          <Box className="w-4 h-4" />
        </button>
      </div>
      
      <div className="grid grid-cols-2 gap-2">
        <div>
          <input
            type="number"
            value={values.top}
            onChange={(e) => handleChange('top', Number(e.target.value))}
            className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"
            placeholder="Top"
          />
        </div>
        <div>
          <input
            type="number"
            value={values.right}
            onChange={(e) => handleChange('right', Number(e.target.value))}
            className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"
            placeholder="Right"
          />
        </div>
        <div>
          <input
            type="number"
            value={values.bottom}
            onChange={(e) => handleChange('bottom', Number(e.target.value))}
            className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"
            placeholder="Bottom"
          />
        </div>
        <div>
          <input
            type="number"
            value={values.left}
            onChange={(e) => handleChange('left', Number(e.target.value))}
            className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"
            placeholder="Left"
          />
        </div>
      </div>
    </div>
  )
}

export const EnhancedPropertiesPanel: React.FC<EnhancedPropertiesPanelProps> = ({
  selectedComponent,
  onComponentUpdate,
  previewMode,
  onPreviewModeChange
}) => {
  const updateContent = (contentUpdates: Record<string, any>) => {
    if (!selectedComponent) return
    
    onComponentUpdate({
      content: {
        ...selectedComponent.content,
        ...contentUpdates
      }
    })
  }

  const updateStyles = (styleUpdates: Record<string, any>) => {
    if (!selectedComponent) return
    
    onComponentUpdate({
      styles: {
        ...selectedComponent.styles,
        ...styleUpdates
      }
    })
  }

  const resetStyles = () => {
    if (!selectedComponent) return
    
    // Reset to default styles based on component type
    const defaultStyles: Record<string, any> = {
      text: { fontSize: '16px', lineHeight: '1.5', color: '#333333', padding: '16px' },
      heading: { fontSize: '32px', fontWeight: 'bold', color: '#1a1a1a', padding: '16px' },
      image: { width: '100%', height: 'auto', borderRadius: '8px' },
      button: { backgroundColor: '#3b82f6', color: '#ffffff', padding: '12px 24px', borderRadius: '6px' },
      divider: { border: 'none', borderTop: '1px solid #e5e7eb', margin: '20px 0' },
      spacer: { height: '40px', width: '100%' }
    }

    onComponentUpdate({
      styles: defaultStyles[selectedComponent.type] || {}
    })
  }

  if (!selectedComponent) {
    return (
      <div className="w-80 bg-gray-800 border-l border-gray-700 flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-700">
          <h2 className="text-white font-semibold text-lg mb-2">Properties</h2>
          <p className="text-gray-400 text-sm">Select a component to edit its properties</p>
        </div>

        {/* Preview Mode Selector */}
        <div className="p-6 border-b border-gray-700">
          <h3 className="text-white font-medium mb-3">Preview Mode</h3>
          <div className="flex items-center bg-gray-700 rounded-lg p-1">
            {[
              { mode: 'desktop' as const, icon: Monitor, label: 'Desktop' },
              { mode: 'tablet' as const, icon: Tablet, label: 'Tablet' },
              { mode: 'mobile' as const, icon: Smartphone, label: 'Mobile' }
            ].map(({ mode, icon: Icon, label }) => (
              <button
                key={mode}
                onClick={() => onPreviewModeChange(mode)}
                className={`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  previewMode === mode
                    ? 'bg-teal-500 text-white'
                    : 'text-gray-300 hover:text-white hover:bg-gray-600'
                }`}
              >
                <Icon className="w-4 h-4 mr-1" />
                {label}
              </button>
            ))}
          </div>
        </div>

        {/* Empty State */}
        <div className="flex-1 flex items-center justify-center p-6">
          <div className="text-center">
            <Layers className="w-12 h-12 text-gray-600 mx-auto mb-4" />
            <p className="text-gray-400 text-sm">No component selected</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-80 bg-gray-800 border-l border-gray-700 flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-700">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-white font-semibold text-lg">Properties</h2>
          <button
            onClick={resetStyles}
            className="p-2 text-gray-400 hover:text-white transition-colors"
            title="Reset Styles"
          >
            <RotateCcw className="w-4 h-4" />
          </button>
        </div>
        <p className="text-gray-400 text-sm capitalize">
          {selectedComponent.type.replace('-', ' ')} Component
        </p>
      </div>

      {/* Properties Sections */}
      <div className="flex-1 overflow-y-auto">
        {/* Content Properties */}
        {(selectedComponent.type === 'text' || selectedComponent.type === 'heading' || selectedComponent.type === 'button') && (
          <PropertySection id="content" title="Content" icon={<Type className="w-4 h-4" />}>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Text
              </label>
              <textarea
                value={selectedComponent.content?.text || ''}
                onChange={(e) => updateContent({ text: e.target.value })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white text-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                rows={3}
              />
            </div>
            
            {selectedComponent.type === 'button' && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Link URL
                </label>
                <input
                  type="url"
                  value={selectedComponent.content?.href || ''}
                  onChange={(e) => updateContent({ href: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white text-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  placeholder="https://example.com"
                />
              </div>
            )}
          </PropertySection>
        )}

        {/* Image Properties */}
        {selectedComponent.type === 'image' && (
          <PropertySection id="image" title="Image" icon={<Image className="w-4 h-4" />}>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Image URL
              </label>
              <input
                type="url"
                value={selectedComponent.content?.src || ''}
                onChange={(e) => updateContent({ src: e.target.value })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white text-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                placeholder="https://example.com/image.jpg"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Alt Text
              </label>
              <input
                type="text"
                value={selectedComponent.content?.alt || ''}
                onChange={(e) => updateContent({ alt: e.target.value })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white text-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                placeholder="Describe the image"
              />
            </div>
          </PropertySection>
        )}

        {/* Typography Properties */}
        {(selectedComponent.type === 'text' || selectedComponent.type === 'heading' || selectedComponent.type === 'button') && (
          <PropertySection id="typography" title="Typography" icon={<Type className="w-4 h-4" />}>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Font Family
              </label>
              <select
                value={selectedComponent.styles?.fontFamily || 'Arial, sans-serif'}
                onChange={(e) => updateStyles({ fontFamily: e.target.value })}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white text-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              >
                <option value="Arial, sans-serif">Arial</option>
                <option value="Helvetica, sans-serif">Helvetica</option>
                <option value="Georgia, serif">Georgia</option>
                <option value="'Times New Roman', serif">Times New Roman</option>
                <option value="'Courier New', monospace">Courier New</option>
                <option value="Verdana, sans-serif">Verdana</option>
              </select>
            </div>

            <RangeSlider
              label="Font Size"
              value={parseInt(selectedComponent.styles?.fontSize || '16')}
              onChange={(value) => updateStyles({ fontSize: `${value}px` })}
              min={8}
              max={72}
              unit="px"
            />

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Text Align
              </label>
              <div className="grid grid-cols-4 gap-1">
                {['left', 'center', 'right', 'justify'].map((align) => (
                  <button
                    key={align}
                    onClick={() => updateStyles({ textAlign: align })}
                    className={`py-2 px-3 text-xs rounded transition-colors ${
                      selectedComponent.styles?.textAlign === align
                        ? 'bg-teal-500 text-white'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    {align.charAt(0).toUpperCase() + align.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            <ColorPicker
              label="Text Color"
              value={selectedComponent.styles?.color || '#333333'}
              onChange={(color) => updateStyles({ color })}
            />
          </PropertySection>
        )}

        {/* Appearance Properties */}
        <PropertySection id="appearance" title="Appearance" icon={<Palette className="w-4 h-4" />}>
          <ColorPicker
            label="Background Color"
            value={selectedComponent.styles?.backgroundColor || 'transparent'}
            onChange={(backgroundColor) => updateStyles({ backgroundColor })}
          />

          <RangeSlider
            label="Border Radius"
            value={parseInt(selectedComponent.styles?.borderRadius || '0')}
            onChange={(value) => updateStyles({ borderRadius: `${value}px` })}
            min={0}
            max={50}
            unit="px"
          />

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Border
            </label>
            <input
              type="text"
              value={selectedComponent.styles?.border || 'none'}
              onChange={(e) => updateStyles({ border: e.target.value })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white text-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              placeholder="1px solid #ccc"
            />
          </div>

          <RangeSlider
            label="Opacity"
            value={parseFloat(selectedComponent.styles?.opacity || '1')}
            onChange={(value) => updateStyles({ opacity: value.toString() })}
            min={0}
            max={1}
            step={0.1}
          />
        </PropertySection>

        {/* Layout Properties */}
        <PropertySection id="layout" title="Layout" icon={<Layout className="w-4 h-4" />}>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Width
            </label>
            <input
              type="text"
              value={selectedComponent.styles?.width || 'auto'}
              onChange={(e) => updateStyles({ width: e.target.value })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white text-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              placeholder="auto, 100%, 300px"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Height
            </label>
            <input
              type="text"
              value={selectedComponent.styles?.height || 'auto'}
              onChange={(e) => updateStyles({ height: e.target.value })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white text-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              placeholder="auto, 100px, 50vh"
            />
          </div>
        </PropertySection>
      </div>
    </div>
  )
}
